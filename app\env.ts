import "server-only";
import { z } from "zod";

// Define the schema for the environment variables
const envSchema = z.object({
  NODE_ENV: z.enum(["development", "production"]),
  APP_URL: z.string(),
  PORT: z.string().regex(/^\d+$/).transform(Number).default("80"), // Defaults to HTTP port

  N8N_HOST: z.string(),
  N8N_HOST_DEV: z.string(),
  NEXT_PUBLIC_CANDIDATES_API_URL: z.string(),
  NEXT_PUBLIC_CONTACT_WEBHOOK_URL: z.string(),
  NEXT_PUBLIC_CONTACT2_WEBHOOK_URL: z.string(),
  NEXT_PUBLIC_DELETE_WEBHOOK_URL: z.string(),
  NEXT_PUBLIC_COMMENT_WEBHOOK_URL: z.string(),
});

// Parse and validate the environment variables
const parsedEnv = envSchema.safeParse(process.env);
if (!parsedEnv.success) {
  console.error("❌ Invalid environment variables:", parsedEnv.error.format());
  throw new Error("Invalid environment variables.");
}

export const env = parsedEnv.data;
