'use client';

import { useEffect, useState } from 'react';
import { LogOut } from 'lucide-react'; // Using lucide-react for icons
import { Button } from '@/components/ui/button'; // Assuming you have a Button component
import { useRouter, usePathname } from 'next/navigation'; // Import usePathname


interface User {
  name?: string;
  email?: string;
}

import { logoutAction } from '@/lib/actions/auth'; // Import the actual logout action

export default function UserDisplay() {
  const pathname = usePathname(); // Get current pathname

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Logic within useEffect is now structured to avoid an early return.
    if (pathname === '/login') {
      setUser(null); // Ensure user is null if on login page
      setLoading(false); // Ensure loading is false if on login page
      // No early return here; the rest of the effect won't run due to the else block.
    } else {
      // Only fetch user data if not on the login page
      setLoading(true); // Set loading true at the start of effect for non-login pages
      fetch('/api/user/me')
      .then(res => {
        if (res.status === 401) { // Not authenticated
          setUser(null);
          // No need to router.push('/login') here, middleware should handle it
          // or if already on a public page, UserDisplay will just show nothing or login button
          return null; // Stop processing
        }
        if (!res.ok) {
          setUser(null); // Clear user on other errors too
          throw new Error(`Failed to fetch user data, status: ${res.status}`);

        }
        return res.json();
      })
      .then(data => {
        if (data && (data.name || data.email)) {
          setUser(data);
        } else {
          // If data is null (e.g., from 401 return null) or malformed
          setUser(null);
        }
      })
      .catch(err => {
        console.error("Error fetching user data in useEffect:", err);
        setUser(null); // Clear user on any error during fetch
      })
      .finally(() => {
        setLoading(false); // Always set loading to false at the end
      }) // Semicolon removed from here
    }
  }, [pathname]); // Depend only on pathname. Router is not needed for this effect.


  const handleLogout = async () => {
    setIsLoggingOut(true);
    // Call the server action from lib/actions/auth.ts
    // This action itself now handles clearing the httpOnly cookie.
    const result = await logoutAction();

    if (result.success) {
      // Additionally, call the API endpoint to ensure any server-side session (e.g. Keycloak) is also cleared if applicable
      // and to be absolutely sure the cookie is gone from the browser's perspective for the next request.
      try {
        const apiRes = await fetch('/api/auth/logout', { method: 'POST' });
        if (!apiRes.ok) {
          console.warn("API logout endpoint call failed or had no effect, but client-side action succeeded.");
        }
      } catch (apiError) {
        console.warn("Error calling API logout endpoint:", apiError);
      }
      setUser(null); // Clear user state before redirecting

      router.push('/login');
      // Optionally, force a full page reload to ensure all state is cleared:
      // window.location.href = '/login';
    } else {
      console.error("Logout failed:", result.error);
      // Handle logout error (e.g., show a message to the user)
      alert(`Logout failed: ${result.error || 'Unknown error'}`);
    }
    setIsLoggingOut(false);
  };

  // Conditional rendering logic moved after all hooks

  // 1. If on login page, render nothing.
  if (pathname === '/login') {
    return null;
  }

  // 2. Handle loading state (if not on login page)
  if (loading) {
    return (
      <div className="fixed top-4 right-4 text-white text-sm p-2 bg-gray-700/50 backdrop-blur-sm rounded-md shadow-lg animate-pulse">
        Cargando...

      </div>
    );
  }

  // 3. If not loading, not on login page, and no user, render nothing.
  // This covers cases where user is not authenticated on a non-login page.
  if (!user) {
    return null;
  }

  // 4. If not loading, not on login page, and user exists, display user info and logout button.

  return (
    <div className="fixed top-4 right-4 flex items-center space-x-3 p-2 bg-gray-800 bg-opacity-80 backdrop-blur-md rounded-lg shadow-xl text-white z-50">
      <div className="flex flex-col text-right">
        <span className="text-sm font-semibold">{user.name || 'Usuario'}</span>
        {user.email && <span className="text-xs text-gray-300">{user.email}</span>}
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={handleLogout}
        disabled={isLoggingOut}
        className="text-red-400 hover:text-red-300 hover:bg-red-700 hover:bg-opacity-50 transition-colors"
        aria-label="Cerrar sesión"
      >
        {isLoggingOut ? (
          <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <LogOut className="h-5 w-5" />
        )}
      </Button>
    </div>
  );
}
