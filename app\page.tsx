"use client";

import { useEffect, useState, useMemo, useRef } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { useDebounce } from "use-debounce";
import type { Candidate } from "@/lib/types";
import CandidateCard from "@/components/candidate-card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Loader2,
  AlertTriangle,
  Users,
  ChevronDown,
  Filter,
  PanelLeft,
  Brain, // Added Brain icon
} from "lucide-react";
import { cn } from "@/lib/utils";
import { ScrollToTopButton } from "@/components/ui/scroll-to-top-button";
import {
  Sidebar,
  SidebarHeader,
  SidebarTitle,
  SidebarContent,
  SidebarFooter,
} from "@/components/ui/sidebar";
import Image from "next/image";

const etapaTabs = [
  { value: "1", label: "Etapa 1" },
  { value: "2", label: "Etapa 2" },
  { value: "3", label: "Etapa 3" },
  { value: "todos", label: "Todos" },
];

export default function CandidatesPage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [allCandidates, setAllCandidates] = useState<Candidate[]>([]);
  const [filteredCandidates, setFilteredCandidates] = useState<Candidate[]>([]);
  const [activeVacante, setActiveVacante] = useState<string>(
    () => searchParams.get("vacante") || "Todos"
  );
  const [activeEtapaTab, setActiveEtapaTab] = useState<string>(
    () => searchParams.get("etapa") || "1"
  );
  // Rename existing searchTerm to inputValue
  const [inputValue, setInputValue] = useState<string>(() => searchParams.get("search") || "");
  // Create debouncedSearchTerm based on inputValue
  const [debouncedSearchTerm] = useDebounce(inputValue, 500); // 500ms delay
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | "none" | "score_desc">("none"); // Estado para el ordenamiento
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showVacanteFilters, setShowVacanteFilters] = useState<boolean>(false);
  const filterButtonRef = useRef<HTMLDivElement>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(true);
  const [isAISorting, setIsAISorting] = useState<boolean>(false); // New state for AI sorting loader

  const fetchCandidatesData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const apiUrl = process.env.NEXT_PUBLIC_CANDIDATES_API_URL;
      if (!apiUrl) {
        throw new Error(
          "La URL de la API de candidatos no está configurada. Por favor, define NEXT_PUBLIC_CANDIDATES_API_URL en tus variables de entorno."
        );
      }
      const response = await fetch(apiUrl, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache', // For HTTP/1.0 compatibility
          'Expires': '0' // For proxies
        }
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          console.error("Failed to parse error response:", e);
        }
        const errorMessage =
          errorData?.message ||
          response.statusText ||
          `Error ${response.status}`;
        throw new Error(`Error al obtener datos de la API: ${errorMessage}`);
      }
      const data: Candidate[] = await response.json();
      console.log("Fetched data (sample from API in fetchCandidatesData):", data.slice(0, 5).map(c => ({ id: c.id, name: c.nombre, score: c.score })));
      setAllCandidates(data);
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : "Ocurrió un error desconocido al cargar candidatos."
      );
      console.error("Failed to fetch candidates:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCandidatesData();
  }, []);

  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    if (activeVacante === "Todos") {
      params.delete("vacante");
    } else {
      params.set("vacante", activeVacante);
    }

    if (activeEtapaTab === "todos") {
      params.delete("etapa");
    } else {
      params.set("etapa", activeEtapaTab);
    }

    if (debouncedSearchTerm.trim() === "") {
      params.delete("search");
    } else {
      params.set("search", debouncedSearchTerm.trim());
    }

    // Añadir sortOrder a los parámetros de la URL
    if (sortOrder === "none") {
      params.delete("sortOrder");
    } else {
      params.set("sortOrder", sortOrder);
    }

    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
  }, [
    activeVacante,
    activeEtapaTab,
    debouncedSearchTerm,
    sortOrder, // Añadir sortOrder a las dependencias
    router,
    pathname,
    searchParams,
  ]);

  // useEffect para inicializar sortOrder desde la URL al cargar la página
  useEffect(() => {
    const sortOrderFromParams = searchParams.get("sortOrder");
    if (sortOrderFromParams === "asc" || sortOrderFromParams === "desc") {
      setSortOrder(sortOrderFromParams);
    } else {
      setSortOrder("none");
    }
  }, [searchParams]);

  const uniqueVacantes = useMemo(() => {
    const vacantesSet = new Set(allCandidates.map((c) => c.vacante));
    return Array.from(vacantesSet).sort();
  }, [allCandidates]);
 useEffect(() => {
  let candidates = allCandidates;

  // Filter out candidates with etapa === 0
  candidates = candidates.filter((candidate) => candidate.etapa !== 0);

  if (activeVacante !== "Todos") {
    candidates = candidates.filter(
      (candidate) => candidate.vacante === activeVacante
    );

    // ✅ Si hay vacante seleccionada y no se eligió un orden, forzar por score descendente
    if (sortOrder === "none") {
      setSortOrder("score_desc");
      return; // Evita renderizado con sortOrder viejo
    }
  }

  // Etapa
  if (activeEtapaTab !== "todos") {
    const etapaNumber = parseInt(activeEtapaTab, 10);
    candidates = candidates.filter(
      (candidate) => candidate.etapa === etapaNumber
    );
  }

  // Búsqueda
  if (debouncedSearchTerm.trim()) {
    const lowerSearchTerm = debouncedSearchTerm.toLowerCase().trim();
    candidates = candidates.filter(
      (candidate) =>
        (candidate.nombre &&
          candidate.nombre.toLowerCase().includes(lowerSearchTerm)) ||
        (candidate.email &&
          candidate.email.toLowerCase().includes(lowerSearchTerm)) ||
        (candidate.vacante &&
          candidate.vacante.toLowerCase().includes(lowerSearchTerm)) ||
        (candidate.descripcion &&
          candidate.descripcion.toLowerCase().includes(lowerSearchTerm)) ||
        (candidate.dni &&
          candidate.dni.toLowerCase().includes(lowerSearchTerm))
    );
  }

  // Ordenamiento
  const sortedCandidates = sortCandidates(candidates, sortOrder);
  setFilteredCandidates(sortedCandidates);
}, [activeVacante, debouncedSearchTerm, allCandidates, activeEtapaTab, sortOrder]);

  // Función para ordenar candidatos
  const sortCandidates = (candidates: Candidate[], order: "asc" | "desc" | "none" | "score_desc"): Candidate[] => {
    console.log(`Sorting candidates with order: "${order}". Sample input:`, candidates.slice(0, 5).map(c => ({id: c.id, name: c.nombre, score: c.score, created_at: c.created_at })));
    const candidatesCopy = candidates.slice(); // Create a copy to avoid mutating the original array

    if (order === "score_desc") {
      return candidatesCopy.sort((a, b) => {
        const scoreA = a.score ?? -Infinity; // Treat undefined/null scores as lowest
        const scoreB = b.score ?? -Infinity;
        return scoreB - scoreA; // Sorts in descending order of score
      });
    }
    if (order === "asc" || order === "desc") {
      return candidatesCopy.sort((a, b) => {
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return order === "asc" ? dateA - dateB : dateB - dateA;
      });
    }
    // Default order (or "none"), sort by ID or original fetch order if preferred
    return candidatesCopy.sort((a, b) => a.id - b.id);
  };

  const handleSortByAI = async (vacante: string) => {
    const webhookUrl = "http://10.20.62.94:5678/webhook/orderbyai";
    console.log(`Ordenar con IA para la vacante: ${vacante}`);
    setIsAISorting(true); // Set loading state to true
    try {
      const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ vacante_nombre: vacante }),
      });

      if (!response.ok) {
        // Even if not ok, we might want to stop loading unless it's a specific error
        // we want to handle differently. For now, loader stops in finally.
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (response.status === 204) {
        console.log("Respuesta del webhook: 204 No Content. Ordenamiento procesado por IA.");
        alert(`Proceso de IA para "${vacante}" completado (204). Recargando y ordenando por score...`);
        await fetchCandidatesData(); // Re-fetch data
        setSortOrder("score_desc"); // Set sort order to score descending
      } else {
        // If response is not 204, but still ok (e.g. 200 with data, though not expected now)
        // For now, we assume 204 is the success signal for AI processing completion.
        // Other successful statuses might need different handling if the webhook's behavior changes.
        let responseBodyInfo = "No additional data in response.";
        try {
          const result = await response.json();
          console.log("Respuesta del webhook (no 204):", result);
          responseBodyInfo = `Data: ${JSON.stringify(result)}`;
        } catch (e) {
          // Could be empty response or not JSON
          responseBodyInfo = `Response was not JSON or empty: ${await response.text()}`;
        }
        alert(`Respuesta inesperada del webhook para "${vacante}" (Status: ${response.status}). ${responseBodyInfo}`);
      }
    } catch (error) {
      console.error("Error al interactuar con el webhook de IA:", error);
      alert(`Error al procesar con IA para la vacante "${vacante}".`);
    } finally {
      setIsAISorting(false); // Set loading state to false in all cases
    }
  };

  const handleVacanteSelect = (vacante: string) => {
    setActiveVacante(vacante);
    setShowVacanteFilters(false);
  };

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        filterButtonRef.current &&
        !filterButtonRef.current.contains(event.target as Node)
      ) {
        setShowVacanteFilters(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filterButtonRef]);

  const totalCandidates = allCandidates.length;
  const showingCandidates = filteredCandidates.length;

  return (
    <>
      <style jsx>{`
        .no-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .no-scrollbar {
          -ms-overflow-style: none; /* IE and Edge */
          scrollbar-width: none; /* Firefox */
        }
      `}</style>
      <div className="flex min-h-screen  bg-[#18181b]">
        
        <Sidebar isOpen={isSidebarOpen} onOpen={() => setIsSidebarOpen(true)} onClose={() => setIsSidebarOpen(false)}>
          <SidebarHeader>
            <SidebarTitle>Filtros</SidebarTitle>
              <Image
                src="/favicon.ico"
                alt="Logo"
                width={32}
                height={32}
                className="h-8 w-8"
              />
          </SidebarHeader>
          <SidebarContent>
            {/* Barra de Búsqueda */}
            <div className="relative w-full mb-4">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-slate-400 dark:text-slate-500" />
              <Input
                type="search"
                placeholder="Buscar en vista actual..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="pl-9 sm:pl-12 pr-3 sm:pr-4 py-2 sm:py-2.5 w-full text-xs sm:text-base border-slate-600 dark:border-slate-600 focus:ring-sky-500 focus:border-sky-500 dark:bg-slate-700 text-white placeholder-slate-400"
                aria-label="Buscar candidatos"
              />
            </div>

            {/* Filtro de Vacantes */}
            <div className="relative w-full mb-4" ref={filterButtonRef}>
              <Button
                variant="outline"
                onClick={() => setShowVacanteFilters(!showVacanteFilters)}
                className="w-full justify-between dark:bg-slate-700 dark:border-slate-600 dark:text-slate-200 dark:hover:bg-slate-600 text-xs sm:text-sm py-2 sm:py-2.5"
                aria-expanded={showVacanteFilters}
                aria-controls="vacante-filter-list"
              >
                <div className="flex items-center">
                  <Filter className="mr-1.5 sm:mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                  {activeVacante === "Todos"
                    ? "Filtrar Vacante"
                    : activeVacante.length > 15 ? activeVacante.substring(0,12) + "..." : activeVacante }
                </div>
                <ChevronDown
                  className={cn(
                    "ml-1.5 sm:ml-2 h-3.5 w-3.5 sm:h-4 sm:w-4 transition-transform",
                    showVacanteFilters && "rotate-180"
                  )}
                />
              </Button>
              {/* "Ordenar con IA" Button / Loader */}
              {activeVacante !== "Todos" && (
                isAISorting ? (
                  <Button
                    variant="outline"
                    className="w-full justify-center neon-button mt-2 text-xs sm:text-sm py-2 sm:py-2.5" // Added mt-2 for spacing
                    disabled // Disable button when loading
                  >
                    <Loader2 className="mr-1.5 sm:mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4 animate-spin" />
                    Procesando...
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    className="w-full justify-center neon-button mt-2 text-xs sm:text-sm py-2 sm:py-2.5" // Added mt-2 for spacing
                    onClick={() => handleSortByAI(activeVacante)}
                  >
                    <Brain className="mr-1.5 sm:mr-2 h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    Ordenar con IA
                  </Button>
                )

              )}
              {showVacanteFilters && (
                <div
                  id="vacante-filter-list"
                  className="absolute z-20 mt-1 w-full max-h-60 overflow-y-auto rounded-md bg-slate-700 dark:bg-slate-700 border border-slate-600 dark:border-slate-600 shadow-lg no-scrollbar p-1"
                >
                  <Button
                    key="Todos"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleVacanteSelect("Todos")}
                    className={cn(
                      "w-full justify-start px-2 py-1.5 text-xs sm:text-sm rounded-sm dark:text-slate-200 hover:bg-slate-600 dark:hover:bg-slate-600 hover:text-slate-100 dark:hover:text-slate-100",
                      activeVacante === "Todos" &&
                        "bg-sky-600 hover:bg-sky-700 text-white dark:bg-sky-600 dark:hover:bg-sky-700 dark:text-white"
                    )}
                  >
                    Todos
                  </Button>
                  {uniqueVacantes.map((vacante) => (
                    <Button
                      key={vacante}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleVacanteSelect(vacante)}
                      className={cn(
                        "w-full justify-start px-2 py-1.5 text-xs sm:text-sm rounded-sm text-white dark:text-white hover:bg-slate-600 dark:hover:bg-slate-600 hover:text-white dark:hover:text-white",
                        activeVacante === vacante &&
                          "bg-sky-600 hover:bg-sky-700 text-white dark:bg-sky-600 dark:hover:bg-sky-700 dark:text-white"
                      )}
                    >
                      {vacante}
                    </Button>
                  ))}
                </div>
              )}
            </div>

            {/* Tabs para Etapa Filter */}
            <div className="mb-4">
              <Tabs
                value={activeEtapaTab}
                onValueChange={setActiveEtapaTab}
                className="w-full"
              >
                <TabsList className="flex flex-col w-full bg-slate-700 dark:bg-slate-700 p-1 h-auto rounded-md gap-1"> {/* Changed to flex-col and added gap-1 */}

                  {etapaTabs.map((tab) => (
                    <TabsTrigger
                      key={tab.value}
                      value={tab.value}
                      className={cn(
                        "w-full text-slate-300 dark:text-slate-300 data-[state=active]:bg-sky-600 data-[state=active]:text-white dark:data-[state=active]:bg-sky-600 dark:data-[state=active]:text-white", // Removed flex-1, ensured w-full
                        "py-1.5 sm:py-2 text-[11px] min-[400px]:text-xs sm:text-sm rounded-sm"

                      )}
                    >
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            </div>

            {/* Controles de Ordenamiento */}
            <div className="flex flex-col gap-2">
              <span className="text-xs sm:text-sm text-slate-300 dark:text-slate-300 whitespace-nowrap">Ordenar por fecha:</span>
              <div className="flex flex-col items-stretch gap-1 sm:gap-2 w-full"> {/* Changed to flex-col and items-stretch */}

                <Button
                  variant={sortOrder === "asc" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSortOrder("asc")}
                  className={cn(
                    "dark:text-slate-200 dark:border-slate-600 py-1.5 px-2.5 sm:py-2 sm:px-3 text-xs sm:text-sm w-full", // Removed flex-grow, added w-full

                    sortOrder === "asc"
                      ? "bg-sky-600 hover:bg-sky-700 text-white dark:bg-sky-600 dark:hover:bg-sky-700 dark:text-white"
                      : "dark:bg-slate-700 dark:hover:bg-slate-600"
                  )}
                >
                  Asc
                </Button>
                <Button
                  variant={sortOrder === "desc" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSortOrder("desc")}
                  className={cn(
                    "dark:text-slate-200 dark:border-slate-600 py-1.5 px-2.5 sm:py-2 sm:px-3 text-xs sm:text-sm w-full", // Removed flex-grow, added w-full

                    sortOrder === "desc"
                      ? "bg-sky-600 hover:bg-sky-700 text-white dark:bg-sky-600 dark:hover:bg-sky-700 dark:text-white"
                      : "dark:bg-slate-700 dark:hover:bg-slate-600"
                  )}
                >
                  Desc
                </Button>
                <Button
                  variant={sortOrder === "none" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSortOrder("none")}
                  className={cn(
                    "dark:text-slate-200 dark:border-slate-600 py-1.5 px-2.5 sm:py-2 sm:px-3 text-xs sm:text-sm w-full", // Removed flex-grow, added w-full

                    sortOrder === "none"
                      ? "bg-slate-600 hover:bg-slate-700 text-white dark:bg-slate-500 dark:hover:bg-slate-600 dark:text-white"
                      : "dark:bg-slate-700 dark:hover:bg-slate-600"
                  )}
                >
                  Defecto
                </Button>
              </div>
            </div>
          </SidebarContent>
          <SidebarFooter>
             <div className="text-xs text-slate-400 dark:text-slate-400 p-4 text-center">
              {isLoading
                ? "Cargando..."
                : error
                ? " "
                : `Mostrando ${showingCandidates} de ${
                    activeVacante === "Todos"
                      ? totalCandidates
                      : "los seleccionados"
                  } candidatos.`}
            </div>
          </SidebarFooter>
        </Sidebar>
        <div className="flex-1 container mx-auto px-4 py-8 sm:px-6 lg:px-8">
          <header className="mb-6 sm:mb-8 text-center">
            <Button
              variant="ghost"
              size="icon"
              className="sm:hidden absolute top-4 left-4 text-slate-300"
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            >
              <PanelLeft />
            </Button>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight text-slate-100 dark:text-slate-100">
              Portal de Candidatos
            </h1>
            <p className="mt-2 sm:mt-3 text-base sm:text-lg text-slate-300 dark:text-slate-300 max-w-2xl mx-auto px-2 sm:px-0">
              Explora, busca y filtra candidatos para optimizar tu proceso de
              selección.
            </p>
          </header>

          {/* This div is no longer needed as filters are in the sidebar */}
          {/* <div className="mb-6 p-3 sm:p-4 bg-slate-800 dark:bg-slate-800 shadow-lg rounded-lg"> ... </div> */}

          {isLoading && (
            <div className="flex flex-col justify-center items-center py-20 text-center">
              <Loader2 className="h-12 w-12 animate-spin text-sky-400 dark:text-sky-400" />
              <p className="mt-4 text-lg font-medium text-slate-300 dark:text-slate-300">
                Cargando candidatos...
              </p>
            </div>
          )}

          {error && (
            <div className="flex flex-col justify-center items-center py-20 text-center bg-red-900/30 dark:bg-red-900/50 p-6 rounded-lg shadow">
              <AlertTriangle className="h-12 w-12 text-red-400 dark:text-red-400" />
              <p className="mt-4 text-lg font-semibold text-red-300 dark:text-red-300">
                Error al cargar candidatos
              </p>
              <p className="text-sm text-red-400 dark:text-red-400">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-6 bg-red-500 hover:bg-red-600 text-white dark:bg-red-600 dark:hover:bg-red-700"
              >
                Intentar de nuevo
              </Button>
            </div>
          )}

          {!isLoading && !error && (
            <>
              {filteredCandidates.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {filteredCandidates.map((candidate) => (
                    <CandidateCard
                      key={candidate.id}
                      candidate={candidate}
                      onActionSuccess={async () => {
                        await fetchCandidatesData();
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col justify-center items-center py-20 text-center bg-slate-800 dark:bg-slate-800 p-6 rounded-lg shadow">
                  <Users className="h-12 w-12 text-slate-500 dark:text-slate-500" />
                  <p className="mt-4 text-xl font-medium text-slate-300 dark:text-slate-300">
                    No se encontraron candidatos
                  </p>
                  <p className="text-sm text-slate-400 dark:text-slate-400">
                    Intenta ajustar tus filtros o término de búsqueda.
                  </p>
                </div>
              )}
            </>
          )}
          <footer className="text-center mt-16 py-8 border-t border-slate-700 dark:border-slate-700">
            <p className="text-sm text-slate-400 dark:text-slate-400">
              &copy; {new Date().getFullYear()} Portal de Candidatos RRHH.
            </p>
          </footer>
        </div>
        <ScrollToTopButton />
      </div>
    </>
  );
}
