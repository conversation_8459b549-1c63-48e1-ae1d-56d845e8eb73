# Formato de Comentarios con Usuario

## Funcionalidad Implementada

Cuando un usuario envía un comentario en el sistema de candidatos, ahora se incluye automáticamente el nombre del usuario en el formato:

```
Usuario: 'comentario del usuario'
```

## Ejemplos

### Antes (sin nombre de usuario)
```
Este candidato tiene buena experiencia en React
```

### Después (con nombre de usuario)
```
<PERSON>: 'Este candidato tiene buena experiencia en React'
```

## Lógica de Obtención del Nombre

El sistema obtiene el nombre del usuario en el siguiente orden de prioridad:

1. **`user.name`** - Nombre completo del usuario
2. **`user.username`** - Nombre de usuario (preferred_username de Keycloak)
3. **"Usuario"** - Fallback si no se encuentra ningún nombre

## Código Implementado

```typescript
// Formatear el comentario con el nombre del usuario
const userName = user?.name || user?.username || "Usuario"
const formattedComment = `${userName}: '${hrComment.trim()}'`
```

## Ubicación del Cambio

- **Archivo**: `components/candidate-card.tsx`
- **Función**: `handleSendComment`
- **Líneas**: 73-74

## Beneficios

- ✅ Trazabilidad de comentarios
- ✅ Identificación clara del autor
- ✅ Formato consistente
- ✅ Fallback seguro si no hay nombre disponible
