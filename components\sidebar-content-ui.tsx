"use client";

import React, { useState, useMemo, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Filter, ChevronDown, Search as SearchIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Candidate } from "@/lib/types";

interface SidebarContentUIProps {
  allCandidates: Candidate[];
  activeVacante: string;
  onVacanteChange: (vacante: string) => void;
  activeEtapaTab: string;
  onEtapaChange: (etapa: string) => void;
  searchTerm: string;
  onSearchTermChange: (term: string) => void;
  jobTitle?: string; // This will be the main title for the content area
  jobDepartment?: string;
  // No className needed here as this is not the trigger button
}

const etapaTabs = [
  { value: "1", label: "Etapa 1" },
  { value: "2", label: "Etapa 2" },
  { value: "3", label: "Etapa 3" },
  { value: "todos", label: "Todos" },
];

export function SidebarContentUI({
  allCandidates,
  activeVacante,
  onVacanteChange,
  activeEtapaTab,
  onEtapaChange,
  searchTerm,
  onSearchTermChange,
  jobTitle = "Panel de Filtros", // Default title for the content area
  jobDepartment,
}: SidebarContentUIProps) {
  const [showVacanteFilters, setShowVacanteFilters] = useState<boolean>(false);
  const filterButtonRef = useRef<HTMLDivElement>(null);

  const uniqueVacantes = useMemo(() => {
    const vacantesSet = new Set(allCandidates.map((c) => c.vacante));
    return Array.from(vacantesSet).sort();
  }, [allCandidates]);

  const handleVacanteSelect = (vacante: string) => {
    onVacanteChange(vacante);
    setShowVacanteFilters(false);
  };

  // This useEffect handles clicks outside the vacante filter dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        filterButtonRef.current &&
        !filterButtonRef.current.contains(event.target as Node)
      ) {
        setShowVacanteFilters(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filterButtonRef]);

  return (
    // This div replaces the <aside> or the SheetContent's inner div
    // It can be directly styled or wrapped by a Card itself if the whole sidebar should be a card.
    // For now, it will contain the individual cards as before.
    // The parent (SheetContent or a static div) will handle overall background and padding.
    <div className="space-y-6">
      {/* Job Title / Department - This might be handled by SheetHeader in Sheet mode, or a static header in fixed mode */}
      {/* For consistency, let's make it a Card here, and the parent can decide padding etc. */}
      <Card className="bg-slate-850 dark:bg-slate-850 border-slate-700 dark:border-slate-700">
        <CardHeader className="pb-2 pt-4 px-4">
          <CardTitle className="text-xl font-semibold text-slate-100 dark:text-slate-100">
            {jobTitle}
          </CardTitle>
        </CardHeader>
        {jobDepartment && (
          <CardContent className="px-4 pb-3">
            <p className="text-sm text-slate-300 dark:text-slate-300">
              {jobDepartment}
            </p>
          </CardContent>
        )}
      </Card>

      {/* Search Bar */}
      <Card className="bg-slate-850 dark:bg-slate-850 border-slate-700 dark:border-slate-700">
        <CardHeader className="pb-2 pt-3 px-4">
          <CardTitle className="text-base font-medium text-slate-200 dark:text-slate-200">Buscar</CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-3">
          <div className="relative w-full">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400 dark:text-slate-500" />
            <Input
              type="search"
              placeholder="Buscar candidatos..."
              value={searchTerm}
              onChange={(e) => onSearchTermChange(e.target.value)}
              className="pl-9 pr-3 py-2 w-full text-sm border-slate-600 dark:border-slate-600 focus:ring-sky-500 focus:border-sky-500 dark:bg-slate-700 text-white placeholder-slate-400"
              aria-label="Buscar candidatos"
            />
          </div>
        </CardContent>
      </Card>

      {/* Vacante Filters */}
      <Card className="bg-slate-850 dark:bg-slate-850 border-slate-700 dark:border-slate-700">
        <CardHeader className="pb-2 pt-3 px-4">
          <CardTitle className="text-base font-medium text-slate-200 dark:text-slate-200">Filtrar por Vacante</CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-3">
          <div className="relative w-full" ref={filterButtonRef}>
            <Button
              variant="outline"
              onClick={() => setShowVacanteFilters(!showVacanteFilters)}
              className="w-full justify-between dark:bg-slate-700 dark:border-slate-600 dark:text-slate-200 dark:hover:bg-slate-600 text-xs sm:text-sm py-2"
              aria-expanded={showVacanteFilters}
              aria-controls="vacante-filter-list-content-ui" // Unique ID
            >
              <div className="flex items-center truncate">
                <Filter className="mr-1.5 h-3.5 w-3.5" />
                <span className="truncate">
                  {activeVacante === "Todos"
                    ? "Todas las Vacantes"
                    : activeVacante}
                </span>
              </div>
              <ChevronDown
                className={cn(
                  "ml-1.5 h-3.5 w-3.5 transition-transform",
                  showVacanteFilters && "rotate-180"
                )}
              />
            </Button>
            {showVacanteFilters && (
              <div
                id="vacante-filter-list-content-ui" // Unique ID
                className="absolute z-20 mt-1 w-full max-h-60 overflow-y-auto rounded-md bg-slate-700 dark:bg-slate-700 border border-slate-600 dark:border-slate-600 shadow-lg no-scrollbar p-1"
              >
                <Button
                  key="Todos-filter-content-ui"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVacanteSelect("Todos")}
                  className={cn(
                    "w-full justify-start px-2 py-1.5 text-xs sm:text-sm rounded-sm dark:text-slate-200 hover:bg-slate-600 dark:hover:bg-slate-600 hover:text-slate-100 dark:hover:text-slate-100",
                    activeVacante === "Todos" &&
                      "bg-sky-600 hover:bg-sky-700 text-white dark:bg-sky-600 dark:hover:bg-sky-700 dark:text-white"
                  )}
                >
                  Todas las Vacantes
                </Button>
                {uniqueVacantes.map((vacante) => (
                  <Button
                    key={vacante}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleVacanteSelect(vacante)}
                    className={cn(
                      "w-full justify-start px-2 py-1.5 text-xs sm:text-sm rounded-sm text-white dark:text-white hover:bg-slate-600 dark:hover:bg-slate-600 hover:text-white dark:hover:text-white truncate",
                      activeVacante === vacante &&
                        "bg-sky-600 hover:bg-sky-700 text-white dark:bg-sky-600 dark:hover:bg-sky-700 dark:text-white"
                    )}
                    title={vacante}
                  >
                    <span className="truncate">{vacante}</span>
                  </Button>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Etapa Tabs */}
      <Card className="bg-slate-850 dark:bg-slate-850 border-slate-700 dark:border-slate-700">
        <CardHeader className="pb-2 pt-3 px-4">
          <CardTitle className="text-base font-medium text-slate-200 dark:text-slate-200">Etapas</CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-3">
          <Tabs
            value={activeEtapaTab}
            onValueChange={onEtapaChange} // Directly use onEtapaChange
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 bg-slate-700 dark:bg-slate-700 p-1 h-auto">
              {etapaTabs.map((tab) => (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className={cn(
                    "text-slate-300 dark:text-slate-300 data-[state=active]:bg-sky-600 data-[state=active]:text-white dark:data-[state=active]:bg-sky-600 dark:data-[state=active]:text-white",
                    "py-1.5 text-[11px] min-[400px]:text-xs"
                  )}
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
