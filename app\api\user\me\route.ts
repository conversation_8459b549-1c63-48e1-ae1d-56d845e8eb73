import { NextResponse } from 'next/server';
import { getAuthCookie } from '@/lib/auth-cookies';
import { verifyJwt } from '@/lib/server-utils';

export async function GET() {
  try {
    const auth = await getAuthCookie();

    if (!auth) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const decoded = await verifyJwt(auth.accessToken);

    if (!decoded) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Devolvemos los datos del usuario desde el JWT decodificado
    return NextResponse.json({
      name: decoded.name || decoded.preferred_username,
      email: decoded.email,
      username: decoded.preferred_username,
      // puedes añadir más campos aquí si los necesitas en el frontend
    });
  } catch (error) {
    console.error('Error getting user data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
