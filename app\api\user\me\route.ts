import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import type { NextRequest } from 'next/server';

interface UserDataFromToken {
  name?: string;
  email?: string;
  preferred_username?: string;
  // Agrega aquí otros campos que esperas del token de Keycloak
}

export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const tokenCookie = cookieStore.get('sessionToken');

  if (!tokenCookie) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const tokenValue = tokenCookie.value;
    // Asumimos que el tokenValue es una string JSON que representa los datos del usuario
    const userData: UserDataFromToken = JSON.parse(tokenValue);

    // Devolvemos los datos relevantes. 'name' es común, 'preferred_username' es de Keycloak.
    // Puedes ajustar esto según lo que necesites mostrar.
    return NextResponse.json({
      name: userData.name || userData.preferred_username,
      email: userData.email,
      // puedes añadir más campos aquí si los necesitas en el frontend
    });
  } catch (error) {
    console.error('Error parsing session token:', error);
    // Si hay un error al parsear, el token podría estar corrupto o no ser JSON
    // Es buena idea invalidar la cookie o manejar el error de forma más específica si es necesario
    return NextResponse.json({ error: 'Invalid or malformed token data' }, { status: 500 });
  }
}
