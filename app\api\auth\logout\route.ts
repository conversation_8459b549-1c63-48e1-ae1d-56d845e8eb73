import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import type { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  const cookieStore = cookies();

  // Clear the sessionToken cookie
  cookieStore.set('sessionToken', '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: -1, // Expire the cookie immediately
    path: '/',
    sameSite: 'lax', // Or 'strict'
  });

  // Optionally, you might want to call a Keycloak logout endpoint here as well
  // if you need to invalidate the session on the Keycloak server.
  // This depends on your Keycloak setup and requirements.
  // Example (conceptual):
  // const keycloakLogoutUrl = `${process.env.KEYCLOAK_ISSUER}/protocol/openid-connect/logout`;
  // await fetch(keycloakLogoutUrl, { method: 'POST', body: new URLSearchParams({ client_id: process.env.KEYCLOAK_CLIENT_ID, refresh_token: ... }) });
  // Note: Managing refresh tokens and client secrets for this would add complexity.

  return NextResponse.json({ success: true, message: 'Logged out successfully' });
}
