import { z } from "zod";

const usernameValidator = z
  .string({ required_error: "Debe ingresar un nombre de usuario" })
  .min(1, "El nombre de usuario es requerido")
  .max(25, "El nombre de usuario no puede superar los 20 caracteres")
  .trim();
export const loginFormSchema = z.object({
  username: usernameValidator,
  password: z
    .string({ required_error: "Ingrese su contraseña" })
    .min(1, "Ingrese una contraseña válida"),
  rememberMe: z.boolean().default(false).optional(),
});
export const userKeycloakSchema = z.object({
  sub: z.string(),
  name: z.string(),
  preferred_username: z.string(),
  given_name: z.string(),
  family_name: z.string(),
  operator_id: z.array(z.string()).optional(),
  email: z.string().optional(),
  avatar: z.string().optional(),
  campaigns_id: z.array(z.string()).optional(),
  operators_id: z.array(z.string()).optional(),
});

export type UserKeycloakAPI = z.infer<typeof userKeycloakSchema>;
