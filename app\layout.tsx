"use client"
import "./globals.css";
import React from "react";
import { cn } from "@/lib/utils";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import UserDisplay from "@/components/user-display"; // Import UserDisplay
import ProtectedRoute from "@/components/auth/protected-route";



export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const [queryClient] = React.useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
            refetchOnWindowFocus: false,
            gcTime: 1000 * 60 * 5, // 5 minutes
          },
          mutations: {
            retry: false,
          },
        },
      })
  )
  return (
    <html lang='es'>
      <meta
        name='viewport'
        content='width=device-width, initial-scale=1.0'
      ></meta>
      <head>
        <link rel='icon' href='/favicon.ico' sizes='any' />
      </head>
      <body
        className={cn(` h-dvh w-dvw max-h-dvh max-w-dvw`)}
      >
          <QueryClientProvider client={queryClient}>
              <UserDisplay /> {/* Add UserDisplay component here */}
              <ProtectedRoute>
                {children}
              </ProtectedRoute>
          </QueryClientProvider>
      </body>
    </html>
  )
}