"use client";

import * as React from "react";
import { ArrowUpIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function ScrollToTopButton({
  className,
  ...props
}: React.HTMLAttributes<HTMLButtonElement>) {
  const [isVisible, setIsVisible] = React.useState(false);

  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  React.useEffect(() => {
    window.addEventListener("scroll", toggleVisibility);
    return () => {
      window.removeEventListener("scroll", toggleVisibility);
    };
  }, []);

  return (
    <Button
      variant="outline"
      size="icon"
      className={cn(
        "fixed bottom-8 right-8 z-50 transition-all duration-300 ease-in-out",
        "bg-slate-700 hover:bg-slate-600 text-slate-200 border-slate-600", // Estilos para modo oscuro y normal
        "dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-slate-200 dark:border-slate-600", // Asegura consistencia en modo oscuro
        "rounded-full p-3 shadow-lg hover:shadow-xl transform hover:scale-110", // Forma redonda, padding, sombra y efecto hover
        isVisible ? "opacity-100 scale-100" : "opacity-0 scale-90 pointer-events-none",
        className
      )}
      onClick={scrollToTop}
      aria-label="Scroll to top"
      {...props}
    >
      <ArrowUpIcon className="h-5 w-5" /> {/* Icono ligeramente más grande */}
    </Button>
  );
}
