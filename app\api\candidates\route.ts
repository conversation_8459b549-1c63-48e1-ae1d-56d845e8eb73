import { NextResponse } from "next/server"
import type { Candidate } from "@/lib/types"

// TODO: The candidate list below is static and in-memory.
// Operations like adding comments or deleting candidates are currently simulated
// (e.g., via webhooks or console logs) and are not persisted in this data source.
// For true persistence, a proper database integration and corresponding API handlers
// (e.g., for POST, PUT, DELETE to /api/candidates/[id]) would be required.
// The current DELETE operation, for instance, is handled by a webhook and does not
// modify this `candidatesData` array.
const candidatesData: Candidate[] = [
  {
    id: 1,
    nombre: "<PERSON>",
    numero: "123456789",
    email: "<EMAIL>",
    direccion: "Calle Falsa 123",
    dni: "12345678A",
    linkedlin: "https://linkedin.com/in/juanperez",
    curriculum: "https://example.com/cv/juanperez.pdf",
    etapa: 1,
    vacante: "Desarrollador Frontend",
    source: "Portal de Empleo",
    descripcion: "Experiencia en React y Next.js.",
    salario: "45000",
    "actualizado el dia": "2024-07-01",
    comentario_de_rrhh: "<PERSON>: 'Candidato con muy buena experiencia en React'\nCarlos Ruiz: 'Revisé su portfolio, muy impresionante'",
    created_at: "2024-06-15",
  },
  {
    id: 2,
    nombre: "Ana Garcia",
    numero: "987654321",
    email: "<EMAIL>",
    direccion: "Avenida Siempre Viva 742",
    dni: "87654321B",
    linkedlin: "https://linkedin.com/in/anagarcia",
    curriculum: "https://example.com/cv/anagarcia.pdf",
    etapa: 2,
    vacante: "Diseñador UX/UI",
    source: "Referencia Interna",
    descripcion: "Portfolio impresionante, habilidades en Figma.",
    salario: "50000",
    "actualizado el dia": "2024-07-05",
    comentario_de_rrhh: "Ana López: 'Entrevista inicial muy positiva'\nCarlos Ruiz: 'Excelente candidata para el puesto'",
    created_at: "2024-06-20",
  },
  {
    id: 3,
    nombre: "Carlos Rodriguez",
    numero: "555123456",
    email: "<EMAIL>",
    direccion: "Plaza Mayor 1",
    dni: "11223344C",
    linkedlin: "https://linkedin.com/in/carlosrodriguez",
    curriculum: "https://example.com/cv/carlosrodriguez.pdf",
    etapa: 1,
    vacante: "Desarrollador Backend",
    source: "LinkedIn",
    descripcion: "Experto en Node.js y bases de datos NoSQL.",
    salario: "55000",
    "actualizado el dia": "2024-07-10",
    comentario_de_rrhh: "Pendiente de revisión técnica.",
    created_at: "2024-07-01",
  },
  {
    id: 4,
    nombre: "Laura Martinez",
    numero: "555654321",
    email: "<EMAIL>",
    direccion: "Calle Luna 23",
    dni: "44556677D",
    linkedlin: "https://linkedin.com/in/lauramartinez",
    curriculum: "https://example.com/cv/lauramartinez.pdf",
    etapa: 3,
    vacante: "Desarrollador Frontend",
    source: "Portal de Empleo",
    descripcion: "Conocimientos de Vue.js y Tailwind CSS.",
    salario: "48000",
    "actualizado el dia": "2024-07-12",
    comentario_de_rrhh: "Pasó la prueba técnica.",
    created_at: "2024-06-10",
  },
  {
    id: 5,
    nombre: "Pedro Sanchez",
    numero: "555111222",
    email: "<EMAIL>",
    direccion: "Avenida del Sol 5",
    dni: "99887766E",
    linkedlin: "https://linkedin.com/in/pedrosanchez",
    curriculum: "https://example.com/cv/pedrosanchez.pdf",
    etapa: 1,
    vacante: "Analista de Datos",
    source: "Recomendación",
    descripcion: "Experiencia con Python, SQL y Power BI.",
    salario: "52000",
    "actualizado el dia": "2024-07-15",
    comentario_de_rrhh: "Muy buen CV.",
    created_at: "2024-07-05",
  },
]

export async function GET() {
  // await new Promise(resolve => setTimeout(resolve, 500)); // Simular delay
  return NextResponse.json(candidatesData)
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { candidateId, comment } = body;

    if (!candidateId || comment === undefined) { // comment can be an empty string
      return NextResponse.json({ error: "Invalid request: candidateId and comment are required" }, { status: 400 });
    }

    console.log("Received comment for candidate:", candidateId, "comment:", comment);

    // In a real application, you would save this comment to a database or other storage.
    // For now, we just log it and return a success response.

    return NextResponse.json({ message: "Comment received" }, { status: 200 });
  } catch (error) {
    console.error("Error processing request:", error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: "Invalid JSON format in request body" }, { status: 400 });
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
