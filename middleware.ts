import { NextRequest, NextResponse } from "next/server"
import { AuthTokens, getAuthCookie, refreshTokens } from "@/lib/auth-cookies"
import { isTokenExpired, verifyJwt } from "./lib/server-utils"

const AUTH_COOKIE = "session"
const REFRESH_COOKIE = "refresh_token"
const REDIRECT_COOKIE_NAME = "redirect_path"

// Función para establecer cookies en el edge
function setEdgeAuthCookie(response: NextResponse, tokens: AuthTokens) {
  const expires = new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 horas
  const refreshExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 días

  const value = `${tokens.token_type} ${tokens.access_token}`
  response.cookies.set(AUTH_COOKIE, value, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires,
  })
  response.cookies.set(REFRESH_COOKIE, tokens.refresh_token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires: refreshExpires,
  })
}

function clearEdgeAuthCookie(response: NextResponse) {
  response.cookies.delete(AUTH_COOKIE)
  response.cookies.delete(REFRESH_COOKIE)
}

export async function middleware(request: NextRequest) {
  const isPreflight = request.method === "OPTIONS"
  if (isPreflight) return NextResponse.next()

  if (request.nextUrl.pathname === "/") return NextResponse.next()

  const isApiRequest = request.nextUrl.pathname.startsWith("/api")
  const auth = await getAuthCookie()

  if (!auth) {
    if (isApiRequest) return NextResponse.next()
    return redirectToLogin(request)
  }

  const decoded = await verifyJwt(auth.accessToken)
  if (!decoded) {
    return redirectToLogin(request)
  }
  
  let response = NextResponse.next()

  if (isTokenExpired(decoded)) {
    const refreshed = await refreshTokens(
      auth.refresh_token,
      async (tokens) => {
        setEdgeAuthCookie(response, tokens)
        response.headers.set(
          "Authorization",
          `${tokens.token_type} ${tokens.access_token}`
        )
        return Promise.resolve()
      }
    )

    if (!refreshed) {
      const res = redirectToLogin(request)
      clearEdgeAuthCookie(res)
      return res
    }
  } else {
    response.headers.set("Authorization", `${auth.tokenType} ${auth.accessToken}`)
  }
  return response
}

function redirectToLogin(request: NextRequest) {
  const res = NextResponse.redirect(new URL("/login", request.url))
  res.cookies.set(REDIRECT_COOKIE_NAME, request.nextUrl.pathname + request.nextUrl.search, {
    httpOnly: true,
    secure: false,
    maxAge: 60 * 5,
    path: "/",
    sameSite: "lax",
  })
  return res
}

export const config = {
  matcher: [
    "/((?!login|register|_next|favicon.ico|static|images|privacy-policy).*)",
  ],
}
