export type KeycloakRoles = "supervisor" | "admin" | "operador" | "abm"

export interface KeycloakRolesResponseAuthApi {
  id: string,
  name: string,
  description: string,
  containerId: string,
  composite: boolean,
  clientRole: boolean
}

export interface ClientRoleMapping {
  id: string
  name: string
  description: string
  composite: boolean
  clientRole: boolean
  containerId: string
}

export interface ClientMapping {
  id: string
  client: string
  mappings: ClientRoleMapping[]
}
export interface KeycloakUser {
  id: string
  username: string
  firstName?: string
  lastName?: string
  emailVerified: boolean
  attributes?: {
    LDAP_ENTRY_DN?: string[]
    LDAP_ID?: string[]
    createTimestamp?: string[]
    modifyTimestamp?: string[]
    [key: string]: string[] | undefined
  }
  origin?: string
  createdTimestamp: number
  enabled: boolean
  totp: boolean
  federationLink?: string
  disableableCredentialTypes: string[]
  email?: string
  requiredActions: string[]
  notBefore: number
  access: {
    manageGroupMembership: boolean
    view: boolean
    mapRoles: boolean
    impersonate: boolean
    manage: boolean
  }
  roles: KeycloakRoles[]
}

export interface KeycloakUserWithDetails extends KeycloakUser {
  userProfileMetadata: {
    attributes: {
      name: string
      displayName: string
      required: boolean
      readOnly: boolean
      annotations?: Record<string, unknown>
      validators?: Record<string, Record<string, string | boolean>>
      group?: string
      multivalued: boolean
    }[]
    groups: {
      name: string
      displayHeader: string
      displayDescription: string
    }[]
  }
}

export interface KeycloakUserRole {
  id: string,
  name: KeycloakRoles,
  description: string,
  composite: boolean,
  clientRole: boolean,
  containerId: string
}

export interface KeycloakUserSession {
  id: string,
  username: string,
  userId: string,
  ipAddress: string,
  started: number,
  lastAccess: number,
  clients: {
    additionalProp1: string,
    additionalProp2: string,
    additionalProp3: string
  }
}