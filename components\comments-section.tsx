"use client"

import { useState } from "react"
import { Comment } from "@/lib/types"
import { useAuth } from "@/hooks/use-auth"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { 
  MessageSquare, 
  Edit, 
  Trash2, 
  Save, 
  X,
  Plus
} from "lucide-react"

interface CommentsSectionProps {
  candidateId: number
  comentario_de_rrhh?: string
  onCommentsUpdate?: (comments: Comment[]) => void
}

export default function CommentsSection({
  candidateId,
  comentario_de_rrhh,
  onCommentsUpdate
}: CommentsSectionProps) {
  const [newComment, setNewComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { user } = useAuth()

  // Función para parsear comentarios desde el campo comentario_de_rrhh
  const parseCommentsFromDB = (comentarios: string): Comment[] => {
    if (!comentarios || comentarios.trim() === '') return []

    const lines = comentarios.split('\n').filter(line => line.trim() !== '')
    return lines.map((line, index) => {
      // Intentar extraer el formato "usuario: comentario" (sin comillas)
      const match = line.match(/^(.+?):\s*(.+)$/)
      if (match) {
        return {
          id: `db_comment_${index}`,
          text: match[2],
          author: match[1],
          timestamp: new Date().toISOString(), // No tenemos timestamp real de la DB
        }
      } else {
        return {
          id: `db_comment_${index}`,
          text: line,
          author: "Usuario",
          timestamp: new Date().toISOString(),
        }
      }
    })
  }

  // Estado local de comentarios parseados desde la DB
  const [comments, setComments] = useState<Comment[]>(() =>
    parseCommentsFromDB(comentario_de_rrhh || '')
  )

  const generateCommentId = () => {
    return `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // Función de prueba simplificada para agregar
  const testSimplePost = () => {
    if (!newComment.trim()) {
      alert("Escribe un comentario primero")
      return
    }

    const userName = user?.name || user?.username || "Usuario"
    const formattedComment = `${userName}: ${newComment.trim()}`
    const url = process.env.NEXT_PUBLIC_COMMENT_WEBHOOK_URL || "http://10.20.62.94:5678/webhook/comment"

    const payload = {
      candidateId: candidateId,
      comment: formattedComment
    }

    console.log("=== TEST SIMPLE POST ===")
    console.log("URL:", url)
    console.log("candidateId:", candidateId)
    console.log("comment:", formattedComment)
    console.log("payload:", payload)
    console.log("JSON string:", JSON.stringify(payload))

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    })
    .then(response => {
      console.log("Response status:", response.status)
      console.log("Response ok:", response.ok)
      return response.text()
    })
    .then(text => {
      console.log("Response text:", text)
      alert(`Test enviado. Status visible en consola.`)
    })
    .catch(error => {
      console.error("Error:", error)
      alert(`Error: ${error.message}`)
    })
  }

  // Función de prueba para eliminar
  const testDeleteComment = () => {
    if (comments.length === 0) {
      alert("No hay comentarios para eliminar")
      return
    }

    const firstComment = comments[0]
    const url = process.env.NEXT_PUBLIC_COMMENT_DELETE_WEBHOOK_URL || "http://10.20.62.94:5678/webhook/comment_delete"

    const payload = {
      candidateId: candidateId,
      comment: `${firstComment.author}: ${firstComment.text}`
    }

    console.log("=== TEST DELETE COMMENT ===")
    console.log("URL:", url)
    console.log("candidateId:", candidateId)
    console.log("comment to delete:", `${firstComment.author}: ${firstComment.text}`)
    console.log("payload:", payload)
    console.log("JSON string:", JSON.stringify(payload))

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    })
    .then(response => {
      console.log("Delete response status:", response.status)
      console.log("Delete response ok:", response.ok)
      return response.text()
    })
    .then(text => {
      console.log("Delete response text:", text)
      alert(`Test delete enviado. Status visible en consola.`)
    })
    .catch(error => {
      console.error("Delete error:", error)
      alert(`Error: ${error.message}`)
    })
  }

  // Función de prueba para editar
  const testEditComment = () => {
    if (comments.length === 0) {
      alert("No hay comentarios para editar")
      return
    }

    const firstComment = comments[0]
    const url = process.env.NEXT_PUBLIC_COMMENT_EDIT_WEBHOOK_URL || "http://10.20.62.94:5678/webhook/comment_edit"

    const payload = {
      candidateId: candidateId,
      oldComment: `${firstComment.author}: ${firstComment.text}`,
      newComment: `${firstComment.author}: ${firstComment.text} (editado)`
    }

    console.log("=== TEST EDIT COMMENT ===")
    console.log("URL:", url)
    console.log("candidateId:", candidateId)
    console.log("oldComment:", `${firstComment.author}: ${firstComment.text}`)
    console.log("newComment:", `${firstComment.author}: ${firstComment.text} (editado)`)
    console.log("payload:", payload)
    console.log("JSON string:", JSON.stringify(payload))

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    })
    .then(response => {
      console.log("Edit response status:", response.status)
      console.log("Edit response ok:", response.ok)
      return response.text()
    })
    .then(text => {
      console.log("Edit response text:", text)
      alert(`Test edit enviado. Status visible en consola.`)
    })
    .catch(error => {
      console.error("Edit error:", error)
      alert(`Error: ${error.message}`)
    })
  }

  const handleAddComment = async () => {
    if (!newComment.trim()) {
      console.log("Comentario vacío, no se envía")
      return
    }

    setIsSubmitting(true)

    const userName = user?.name || user?.username || "Usuario"
    const comment: Comment = {
      id: generateCommentId(),
      text: newComment.trim(),
      author: userName,
      timestamp: new Date().toISOString(),
    }

    try {
      const apiUrl = process.env.NEXT_PUBLIC_COMMENT_WEBHOOK_URL || "http://10.20.62.94:5678/webhook/comment"
      const formattedComment = `${userName}: ${newComment.trim()}`

      const payload = {
        candidateId: candidateId,
        comment: formattedComment
      }

      console.log("=== ENVIANDO COMENTARIO ===")
      console.log("URL:", apiUrl)
      console.log("Payload:", payload)
      console.log("JSON:", JSON.stringify(payload))

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      })

      console.log("Response status:", response.status)
      console.log("Response ok:", response.ok)

      if (response.ok) {
        // Actualizar la lista local de comentarios
        const updatedComments = [...comments, comment]
        setComments(updatedComments)
        if (onCommentsUpdate) {
          onCommentsUpdate(updatedComments)
        }
        setNewComment("")
        console.log("Comentario enviado exitosamente")
      } else {
        throw new Error("Error al enviar comentario")
      }
    } catch (error) {
      console.error("Error adding comment:", error)
      alert("Error al enviar el comentario. Intente nuevamente.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEditComment = (commentId: string) => {
    const updatedComments = comments.map(comment =>
      comment.id === commentId
        ? { ...comment, isEditing: true }
        : { ...comment, isEditing: false }
    )
    setComments(updatedComments)
    if (onCommentsUpdate) {
      onCommentsUpdate(updatedComments)
    }
  }

  const handleSaveEdit = async (commentId: string, newText: string) => {
    if (!newText.trim()) return

    try {
      // Encontrar el comentario original
      const originalComment = comments.find(c => c.id === commentId)
      if (!originalComment) {
        console.error("Comentario no encontrado")
        return
      }

      // Enviar edición al webhook
      const editUrl = process.env.NEXT_PUBLIC_COMMENT_EDIT_WEBHOOK_URL || "http://10.20.62.94:5678/webhook/comment_edit"
      const editPayload = {
        candidateId: candidateId,
        oldComment: `${originalComment.author}: ${originalComment.text}`,
        newComment: `${originalComment.author}: ${newText.trim()}`
      }

      console.log("=== EDITANDO COMENTARIO ===")
      console.log("URL:", editUrl)
      console.log("Payload:", editPayload)
      console.log("JSON:", JSON.stringify(editPayload))

      const response = await fetch(editUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(editPayload)
      })

      console.log("Edit response status:", response.status)
      console.log("Edit response ok:", response.ok)

      if (response.ok) {
        // Actualizar localmente solo si el webhook fue exitoso
        const updatedComments = comments.map(comment =>
          comment.id === commentId
            ? { ...comment, text: newText.trim(), isEditing: false }
            : comment
        )
        setComments(updatedComments)
        if (onCommentsUpdate) {
          onCommentsUpdate(updatedComments)
        }
        console.log("Comentario editado exitosamente")
      } else {
        throw new Error("Error al editar comentario en el servidor")
      }
    } catch (error) {
      console.error("Error editando comentario:", error)
      alert("Error al editar el comentario. Por favor, intente nuevamente.")
    }
  }

  const handleCancelEdit = (commentId: string) => {
    const updatedComments = comments.map(comment =>
      comment.id === commentId
        ? { ...comment, isEditing: false }
        : comment
    )
    setComments(updatedComments)
    if (onCommentsUpdate) {
      onCommentsUpdate(updatedComments)
    }
  }

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm("¿Está seguro de que desea eliminar este comentario?")) {
      return
    }

    try {
      // Encontrar el comentario a eliminar
      const commentToDelete = comments.find(c => c.id === commentId)
      if (!commentToDelete) {
        console.error("Comentario no encontrado")
        return
      }

      // Enviar eliminación al webhook
      const deleteUrl = process.env.NEXT_PUBLIC_COMMENT_DELETE_WEBHOOK_URL || "http://10.20.62.94:5678/webhook/comment_delete"
      const deletePayload = {
        candidateId: candidateId,
        comment: `${commentToDelete.author}: ${commentToDelete.text}`
      }

      console.log("=== ELIMINANDO COMENTARIO ===")
      console.log("URL:", deleteUrl)
      console.log("Payload:", deletePayload)
      console.log("JSON:", JSON.stringify(deletePayload))

      const response = await fetch(deleteUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(deletePayload)
      })

      console.log("Delete response status:", response.status)
      console.log("Delete response ok:", response.ok)

      if (response.ok) {
        // Eliminar localmente solo si el webhook fue exitoso
        const updatedComments = comments.filter(comment => comment.id !== commentId)
        setComments(updatedComments)
        if (onCommentsUpdate) {
          onCommentsUpdate(updatedComments)
        }
        console.log("Comentario eliminado exitosamente")
      } else {
        throw new Error("Error al eliminar comentario en el servidor")
      }
    } catch (error) {
      console.error("Error eliminando comentario:", error)
      alert("Error al eliminar el comentario. Por favor, intente nuevamente.")
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm text-gray-400">
        <MessageSquare className="h-4 w-4" />
        Comentarios Internos (RH)
      </div>

      {/* Lista de comentarios existentes */}
      {comments.length > 0 && (
        <div className="space-y-3 max-h-60 overflow-y-auto">
          {comments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onEdit={handleEditComment}
              onSave={handleSaveEdit}
              onCancel={handleCancelEdit}
              onDelete={handleDeleteComment}
              formatTimestamp={formatTimestamp}
            />
          ))}
        </div>
      )}

      {/* Agregar nuevo comentario */}
      <div className="space-y-3">
        <Textarea
          placeholder="Agregar un nuevo comentario..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500"
          rows={3}
        />
        <div className="flex gap-2">
          <Button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (!isSubmitting) {
                handleAddComment()
              }
            }}
            disabled={!newComment.trim() || isSubmitting}
            className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            type="button"
          >
            <Plus className="h-4 w-4 mr-2" />
            {isSubmitting ? "Enviando..." : "Agregar Comentario"}
          </Button>

          <Button
            onClick={testSimplePost}
            disabled={!newComment.trim()}
            className="bg-green-600 hover:bg-green-700 disabled:opacity-50"
            type="button"
          >
            Test Add
          </Button>

          <Button
            onClick={testDeleteComment}
            disabled={comments.length === 0}
            className="bg-red-600 hover:bg-red-700 disabled:opacity-50"
            type="button"
          >
            Test Del
          </Button>

          <Button
            onClick={testEditComment}
            disabled={comments.length === 0}
            className="bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50"
            type="button"
          >
            Test Edit
          </Button>
        </div>
      </div>
    </div>
  )
}

interface CommentItemProps {
  comment: Comment
  onEdit: (id: string) => void
  onSave: (id: string, text: string) => void
  onCancel: (id: string) => void
  onDelete: (id: string) => void
  formatTimestamp: (timestamp: string) => string
}

function CommentItem({ 
  comment, 
  onEdit, 
  onSave, 
  onCancel, 
  onDelete, 
  formatTimestamp 
}: CommentItemProps) {
  const [editText, setEditText] = useState(comment.text)

  return (
    <Card className="bg-gray-800 border-gray-600">
      <CardContent className="p-3">
        <div className="flex justify-between items-start mb-2">
          <div className="text-xs text-gray-400">
            <span className="font-medium text-blue-400">{comment.author}</span>
            <span className="ml-2">{formatTimestamp(comment.timestamp)}</span>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(comment.id)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-blue-400"
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(comment.id)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-red-400"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        {comment.isEditing ? (
          <div className="space-y-2">
            <Textarea
              value={editText}
              onChange={(e) => setEditText(e.target.value)}
              className="bg-gray-700 border-gray-500 text-white text-sm"
              rows={2}
            />
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onSave(comment.id, editText)}
                className="h-7 px-2 text-xs border-green-600 text-green-400 hover:bg-green-700"
              >
                <Save className="h-3 w-3 mr-1" />
                Guardar
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCancel(comment.id)}
                className="h-7 px-2 text-xs border-gray-600 text-gray-400 hover:bg-gray-700"
              >
                <X className="h-3 w-3 mr-1" />
                Cancelar
              </Button>
            </div>
          </div>
        ) : (
          <p className="text-sm text-white">{comment.text}</p>
        )}
      </CardContent>
    </Card>
  )
}
