{
  "extends": [
    "eslint:recommended",
    "next",
    "prettier",
    "next/core-web-vitals",
    "next/typescript",
    "plugin:@typescript-eslint/strict",
    "plugin:@typescript-eslint/stylistic",
    "plugin:@tanstack/eslint-plugin-query/recommended"
  ],
  "globals": {
    "React": true
  },
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    // this setting is required to use rules that require type information
    "project": true
  },
  "plugins": [
    "@typescript-eslint",
    "react",
    "react-hooks",
    "@tanstack/eslint-plugin-query"
  ],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": [
      "warn",
      {
        "additionalHooks": "(useRecoilCallback|useRecoilTransaction_UNSTABLE)"
      }
    ],
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off"
  }
}
