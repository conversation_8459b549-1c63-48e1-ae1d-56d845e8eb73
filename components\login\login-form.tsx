"use client";
import * as React from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { loginFormSchema } from "@/lib/schemas";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Link from "next/link";
import { Checkbox } from "@/components/ui/checkbox";
import { Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useMutation } from "@tanstack/react-query";

import type { UserLoggedInWithoutTokens } from "@/types/user";
import { loginAction } from "@/lib/actions/auth";
import { GLOBAL_ICON_SIZE } from "@/lib/constants";
import { StatefulButton } from "../stateful-button";
import {useLocalStorage} from "@/hooks/use-localstorage"
export default function LoginForm() {
  const [showPassword, setShowPassword] = React.useState(false);
  const {setValue} = useLocalStorage()
  const router = useRouter();
  const form = useForm<z.infer<typeof loginFormSchema>>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
    },
  });

  const loginUser = async (
    credentials: z.infer<typeof loginFormSchema>
  ): Promise<UserLoggedInWithoutTokens | null> => {
    try {
      const response = await loginAction({ credentials });
      if (!response.success || !response.data) {
        throw new Error(response.error ?? "Error al iniciar sesión");
      }

      return response.data;
    } catch (error: unknown) {
      console.error(error);

      return null;
    }
  };

  const loginUserCallback = React.useCallback(loginUser, []);

  const mutation = useMutation({
    mutationKey: ["login"],
    mutationFn: loginUserCallback,
    onSuccess: (data)=>{
      if (data) {
        setValue(data)
        router.push('/');
      }
    }
  });

  const onSubmit = (data: z.infer<typeof loginFormSchema>) => {
    mutation.mutate(data);
  };

  const isLoading = mutation.isPending;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="sm:w-full">
          <CardHeader>
            <h3 className="text-center">Iniciar sesión</h3>
            <CardDescription className="text-center">
              Ingresá a tu cuenta para acceder a la aplicación.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col gap-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Ingrese su usuario"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="relative">
                      <Input
                        {...field}
                        type={showPassword ? "text" : "password"}
                        placeholder="Contraseña"
                        disabled={isLoading}
                      />
                      <Button
                        asChild
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-0 p-2 text-muted-foreground hover:bg-transparent"
                        onClick={() => setShowPassword((prev) => !prev)}
                      >
                        {showPassword ? (
                          <EyeOff size={GLOBAL_ICON_SIZE} />
                        ) : (
                          <Eye size={GLOBAL_ICON_SIZE} />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="rememberMe"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Recordarme</FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            <StatefulButton
              type="submit"
              isLoading={isLoading}
              aria-disabled={isLoading}
              className="w-full"
            >
              Ingresar
            </StatefulButton>
            {/* WIP TODO: Add links to register and forgot password pages */}
            <Link
              href="/signup"
              // className='text-sm text-muted-foreground hover:text-foreground hover:underline underline-offset-4'
              className="text-sm text-muted-foreground hover:cursor-not-allowed"

              // prefetch
            >
              Registrarse
            </Link>
            <Link
              href="/forgot-password"
              // className='text-sm text-muted-foreground hover:text-foreground hover:underline underline-offset-4'
              className="text-sm text-muted-foreground hover:cursor-not-allowed"
            >
              ¿Olvidaste tu contraseña?
            </Link>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
