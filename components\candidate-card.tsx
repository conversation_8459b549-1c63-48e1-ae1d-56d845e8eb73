"use client"

import type { Candidate } from "@/lib/types"

import { useState } from "react"
import { useAuth } from "@/hooks/use-auth"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Briefcase,
  CalendarDays,
  FileText,
  Mail,
  MapPin,
  Phone,
  User,
  CircleDollarSign,
  Info,
  X,
  MessageSquare,
  Vault,
  Percent,
} from "lucide-react"

interface CandidateCardProps {
  candidate: Candidate
  onActionSuccess?: () => void
}

export default function CandidateCard({ candidate, onActionSuccess }: CandidateCardProps) {
  const [showDialog, setShowDialog] = useState(false)
  const [dialogTitle, setDialogTitle] = useState("")
  const [dialogMessage, setDialogMessage] = useState("")
  const [isError, setIsError] = useState(false)
  const [actionWasSuccessful, setActionWasSuccessful] = useState<boolean>(false)
  const [showDeleteConfirmDialog, setShowDeleteConfirmDialog] = useState(false)

  const [hrComment, setHrComment] = useState("")
  const { user } = useAuth()

  const handleSendComment = async () => {
    const apiUrl = "http://10.20.62.94:5678/webhook/comment"
    if (!apiUrl) {
      console.error("NEXT_PUBLIC_COMMENT_WEBHOOK_URL is not defined")
      setDialogTitle("Error de Configuración")
      setDialogMessage("La URL para enviar comentarios no está configurada. Contacte al administrador.")
      setIsError(true)
      setShowDialog(true)
      return
    }

    if (!hrComment.trim()) {
      setDialogTitle("Comentario Vacío")
      setDialogMessage("Por favor, ingrese un comentario antes de enviar.")
      setIsError(true)
      setShowDialog(true)
      return
    }

    // Formatear el comentario con el nombre del usuario
    const userName = user?.name || user?.username || "Usuario"
    const formattedComment = `${userName}: '${hrComment.trim()}'`

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ candidateId: candidate.id, comment: formattedComment }),
      })

      if (response.ok) {
        setActionWasSuccessful(true)
        setDialogTitle("Comentario Enviado")
        setDialogMessage( "El comentario ha sido enviado con éxito.")
        setIsError(false)
        setHrComment("")

      } else {
        setActionWasSuccessful(false)
        setDialogTitle("Error al Enviar")
        setDialogMessage( "No se pudo enviar el comentario. Intente más tarde.")
        setIsError(true)
        
      }
    } catch (error) {
      setActionWasSuccessful(false)
      console.error("Error sending comment:", error)
      setDialogTitle("Error de Red")
      setDialogMessage("Ocurrió un error al conectar con el servidor. Verifique su conexión o intente más tarde.")
      setIsError(true)
    }
    setShowDialog(true)
  }

  const isValidUrl = (url: string) => {
    if (!url || typeof url !== "string") return false
    try {
      new URL(url)
      return true
    } catch (_) {
      console.error("Invalid URL:", _)
      return false
    }
  }

  return (
    <>
      <Card className="w-full max-w-2xl bg-gray-900 border-gray-700 text-white">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center gap-2 text-lg font-medium">
            <User className="h-5 w-5" />
            Detalles de {candidate.nombre}
          </CardTitle>
         
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Primera fila - Nombre y Vacante */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <User className="h-4 w-4" />
                Nombre
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white">{candidate.nombre}</span>
               
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Briefcase className="h-4 w-4" />
                Vacante
              </div>
              <div className="flex items-center justify-between">
                
                <span className="text-white">{candidate.vacante}</span>

                
              </div>
            </div>
          </div>

          {/* Segunda fila - Email y Estado */}
          <div className="grid grid-cols-1 w-full gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Mail className="h-4 w-4" />
                Email
              </div>
              <div className="flex items-center justify-between">
                {candidate.email ? (
                  <a
                    href={`mailto:${candidate.email}`}
                    className="text-white hover:text-blue-400 hover:underline break-all"
                  >
                    {candidate.email}
                  </a>
                ) : (
                  <span className="text-gray-400">no encontrado</span>
                )}
               
              </div>
            </div>

          </div>

          {/* Tercera fila - Teléfono y Etapa */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Phone className="h-4 w-4" />
                Teléfono
              </div>
              <span className="text-white">
                {candidate.numero || <span className="text-gray-400">no encontrado</span>}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Vault className="h-4 w-4" />
                Etapa
              </div>
              <span className="text-white">{candidate.etapa}</span>
            </div>
          </div>

          {/* Cuarta fila - Ubicación y Salario */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <MapPin className="h-4 w-4" />
                Ubicación
              </div>
              <span className={candidate.direccion ? "text-white" : "text-gray-400"}>
                {candidate.direccion || "no encontrada"}
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <CircleDollarSign className="h-4 w-4" />
                Salario
              </div>
              <span
                className={candidate.salario && candidate.salario !== "No encontrado" ? "text-white" : "text-gray-400"}
              >
                {candidate.salario && candidate.salario !== "No encontrado" ? candidate.salario : "no especificado"}
              </span>
            </div>
          </div>

          {/* Quinta fila - Fuente y Match */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <Info className="h-4 w-4" />
                Fuente
              </div>
              <span className={candidate.source ? "text-white" : "text-gray-400"}>
                {candidate.source || "no especificada"}
              </span>
            </div>

            {typeof candidate.score === "number" && candidate.score > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <Percent className="h-4 w-4" />
                  Match
                </div>
                <span className="text-green-500">{(candidate.score * 100).toFixed(2)}%</span>
              </div>
            )}
          </div>

          {/* Descripción */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <FileText className="h-4 w-4" />
              Descripción
            </div>
            <div className="text-sm max-h-20 overflow-y-auto">
              <span
                className={
                  candidate.descripcion && candidate.descripcion !== "No encontrado" ? "text-white" : "text-gray-400"
                }
              >
                {candidate.descripcion && candidate.descripcion !== "No encontrado"
                  ? candidate.descripcion
                  : "no encontrada"}
              </span>
            </div>
          </div>

          {/* Comentario RH - Solo si etapa >= 2 */}
          {candidate.etapa >= 2 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-400">
                <MessageSquare className="h-4 w-4" />
                Comentario Interno (RH)
              </div>
              <Textarea
                placeholder={candidate.comentario_de_rrhh || "Agrega un comentario, solo será visible para vos."}
                value={hrComment}
                onChange={(e) => setHrComment(e.target.value)}
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
              />
            </div>
          )}

          <Separator className="bg-gray-700" />

          {/* Información de actualización */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <CalendarDays className="h-4 w-4" />
              Actualizado
            </div>
            <span className="text-gray-400">{candidate["actualizado el dia"]}</span>
          </div>

          {/* Botones de acción */}
          <div className="flex flex-wrap gap-3 pt-4">
            {candidate.curriculum && candidate.curriculum !== "No encontrado" && isValidUrl(candidate.curriculum) ? (
              <Button
                variant="outline"
                className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent"
                asChild
              >
                <a
                  href={candidate.curriculum}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Ver CV
                </a>
              </Button>
            ) : (
              <Button
                variant="outline"
                className="flex-1 border-gray-600 text-gray-400 bg-transparent cursor-not-allowed"
                disabled
              >
                <FileText className="h-4 w-4 mr-2" />
                CV no disp.
              </Button>
            )}

            <Button
              className="flex-1 bg-blue-600 hover:bg-blue-700"
              onClick={async () => {
                const etapa = candidate.etapa
                const webhookUrl =
                  etapa === 1
                    ? "http://10.20.62.94:5678/webhook/citar"
                    : "http://10.20.62.94:5678/webhook/citar/segunda-entrevista"

                if (!webhookUrl) {
                  console.error("Webhook URL is not defined")
                  setShowDialog(true)
                  setDialogTitle("Error")
                  setDialogMessage("Error al enviar la solicitud de contacto. Intente más tarde.")
                  setIsError(true)
                  return
                }

                try {
                  const response = await fetch(webhookUrl, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ candidateId: candidate.id }),
                  })

                  if (response.ok) {
                    onActionSuccess?.()
                    setActionWasSuccessful(true)
                    setDialogTitle("Contactado!")
                    setDialogMessage("Candidato contactado con exito!")
                    setIsError(false)
                    setShowDialog(true)
                  } else {
                    setActionWasSuccessful(false)
                    console.error("Error sending contact request:", response)
                    setShowDialog(true)
                    setDialogTitle("Error")
                    setDialogMessage("Error al enviar la solicitud de contacto. Intente más tarde.")
                    setIsError(true)
                  }
                } catch (error) {
                  setActionWasSuccessful(false)
                  console.error("Error sending contact request:", error)
                  setShowDialog(true)
                  setDialogTitle("Error")
                  setDialogMessage("Error al enviar la solicitud de contacto. Intente más tarde.")
                  setIsError(true)
                }
              }}
            >
              <Phone className="h-4 w-4 mr-2" />
              Contactar
            </Button>

            <Button variant="destructive" className="flex-1" onClick={() => setShowDeleteConfirmDialog(true)}>
              <X className="h-4 w-4 mr-2" />
              Rechazar
            </Button>

            {candidate.etapa >= 2 && (
              <Button
                variant="outline"
                className="flex-1 border-green-600 text-green-300 hover:bg-green-700 hover:text-green-100 bg-transparent"
                onClick={handleSendComment}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Enviar Comentario
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Dialogs remain the same */}
      <Dialog
        open={showDialog}
        onOpenChange={(isOpen) => {
          if (!isOpen && actionWasSuccessful && !isError) {
            window.location.reload()
          }
          setShowDialog(isOpen)
          if (!isOpen) {
            setActionWasSuccessful(false)
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px] bg-gray-800 border-gray-700">
          <DialogHeader>
            <DialogTitle className={isError ? "text-red-500" : "text-white"}>{dialogTitle}</DialogTitle>
            <DialogDescription className={isError ? "text-red-500" : "text-gray-400"}>
              {dialogMessage}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              onClick={() => {
                setShowDialog(false)
                if (actionWasSuccessful && !isError) {
                  window.location.reload()
                }
              }}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirmDialog} onOpenChange={setShowDeleteConfirmDialog}>
        <DialogContent className="sm:max-w-[425px] bg-gray-800 border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white">Confirmar Rechazo</DialogTitle>
            <DialogDescription className="text-gray-400">
              ¿Está seguro de que desea rechazar a este candidato? Esta acción no se puede deshacer.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirmDialog(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={async () => {
                const webhookUrl = "process.env.NEXT_PUBLIC_DELETE_WEBHOOK_URL"
                if (!webhookUrl) {
                  console.error("NEXT_PUBLIC_DELETE_WEBHOOK_URL is not defined")
                  setDialogTitle("Error de Configuración")
                  setDialogMessage("La URL para eliminar candidatos no está configurada. Contacte al administrador.")
                  setIsError(true)
                  setShowDialog(true)
                  setShowDeleteConfirmDialog(false)
                  return
                }

                try {
                  const response = await fetch(webhookUrl, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ candidateId: candidate.id }),
                  })

                  if (response.ok) {
                    onActionSuccess?.()
                    setActionWasSuccessful(true)
                    setDialogTitle("Sucesso")
                    setDialogMessage("Candidato excluído com sucesso!")
                    setIsError(false)
                    setShowDialog(true)
                  } else {
                    setActionWasSuccessful(false)
                    const responseData = await response.json().catch(() => ({}))
                    console.error("Error sending delete request:", response)
                    setDialogTitle("Error al Eliminar")
                    setDialogMessage(responseData.message || "Error al eliminar contacto. Intente más tarde.")
                    setIsError(true)
                  }
                } catch (error) {
                  setActionWasSuccessful(false)
                  console.error("Error sending delete request:", error)
                  setDialogTitle("Error de Red")
                  setDialogMessage(
                    "Ocurrió un error al conectar con el servidor. Verifique su conexión o intente más tarde.",
                  )
                  setIsError(true)
                }
                setShowDeleteConfirmDialog(false)
              }}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              Confirmar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
