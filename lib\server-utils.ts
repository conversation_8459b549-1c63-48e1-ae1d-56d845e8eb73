import "server-only"
import { DecodedJsonWebToken } from "@/types/user"
import jwt from "jsonwebtoken"

export function verifyJwt(token: string): Promise<DecodedJsonWebToken | null> {
  return new Promise(resolve => {
    const decoded = jwt.decode(token)
    if (!decoded || typeof decoded === "string") resolve(null)
    resolve(decoded as DecodedJsonWebToken)
  })
}
export function isTokenExpired(decoded: DecodedJsonWebToken, marginSeconds = 60): boolean {
  const now = Math.floor(Date.now() / 1000)
  return decoded.exp - now < marginSeconds
}