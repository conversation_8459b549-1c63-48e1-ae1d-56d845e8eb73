"use client";

import { useAuth } from '@/hooks/use-auth';
import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export default function ProtectedRoute({ children, fallback }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const pathname = usePathname();

  // No proteger la página de login
  if (pathname === '/login') {
    return <>{children}</>;
  }

  // Mostrar loading mientras se verifica la autenticación
  if (loading) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        </div>
      )
    );
  }

  // Si no hay usuario autenticado, el middleware debería redirigir
  // pero como fallback adicional, mostramos un mensaje
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Acceso no autorizado</h2>
          <p className="text-gray-600">Redirigiendo al login...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
