"use client";

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface User {
  name?: string;
  email?: string;
  username?: string;
}

interface UseAuthReturn {
  user: User | null;
  loading: boolean;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/user/me');
      
      if (response.status === 401) {
        setUser(null);
        // Solo redirigir si no estamos ya en login
        if (pathname !== '/login') {
          router.push('/login');
        }
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch user');
      }

      const userData = await response.json();
      setUser(userData);
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
      if (pathname !== '/login') {
        router.push('/login');
      }
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
      setUser(null);
      router.push('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // Aún así redirigir al login
      setUser(null);
      router.push('/login');
    }
  };

  useEffect(() => {
    // No verificar auth en la página de login
    if (pathname === '/login') {
      setLoading(false);
      return;
    }

    checkAuth();
  }, [pathname]);

  return {
    user,
    loading,
    logout,
    checkAuth,
  };
}
