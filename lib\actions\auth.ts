"use server";

import { loginFormSchema } from "@/lib/schemas";
import { z } from "zod";
import { getHost } from "@/lib/actions/host";
import { UserLoggedInWithoutTokens } from "@/types/user";
import { cookies } from "next/headers";

export async function loginAction({
  credentials,
}: {
  credentials: z.infer<typeof loginFormSchema>;
}): Promise<{
  success: boolean;
  error: string | null;
  data: UserLoggedInWithoutTokens | null;
}> {
  const { data, success } = loginFormSchema.safeParse(credentials);
  if (!success) {
    return {
      success: false,
      error: "Faltan campos a completar.",
      data: null,
    };
  }

  const url = new URL(`${await getHost("n8n-dev")}/webhook/keycloak-login`);

  try {
    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        username: data.username,
        password: data.password,
      }),
    });

    const result = await res.json();

    if (!res.ok) {
      const message =
        result?.error === "invalid_grant"
          ? "Credenciales inválidas."
          : result?.error_description ||
            "Error en el servidor de autenticación.";
      return {
        success: false,
        error: message,
        data: null,
      };
    }

    // Set the auth cookies using the same format as middleware
    const expires = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 horas
    const refreshExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 días

    const value = `${result.token_type} ${result.access_token}`;
    cookies().set("session", value, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      expires,
      path: "/",
      sameSite: "lax",
    });

    cookies().set("refresh_token", result.refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      expires: refreshExpires,
      path: "/",
      sameSite: "lax",
    });

    return {
      success: true,
      error: null,
      data: {
        sub: result.sub,
        name: result.name,
        preferred_username: result.preferred_username,
        given_name: result.given_name,
        family_name: result.family_name,
        email: result.email,
      },
    };
  } catch (err: unknown) {
    console.error("Error logging in action:", err);
    return {
      success: false,
      error: typeof err === "object" && err !== null && "message" in err
        ? String((err as { message?: unknown }).message)
        : "Ocurrio un error inesperado",
      data: null,
    };
  }
}

export async function logoutAction(): Promise<{
  success: boolean;
  error: string | null;
}> {
  try {
    // Clear both auth cookies
    cookies().set("session", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: -1, // Expire immediately
      path: "/",
      sameSite: "lax",
    });

    cookies().set("refresh_token", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: -1, // Expire immediately
      path: "/",
      sameSite: "lax",
    });

    // Note: If Keycloak session invalidation is needed server-side,
    // it should be called from the /api/auth/logout route, not directly here
    // if it requires secrets or more complex logic not suitable for a direct client action.
    return { success: true, error: null };
  } catch (err) {
    console.error("Error in logoutAction:", err);
    return { success: false, error: "Failed to logout." };
  }
}
