"use client"

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Fi<PERSON>, Brain } from "lucide-react" // Added Brain icon

interface CandidateFiltersProps {
  vacantes: string[]
  selectedVacantes: string[]
  onFilterChange: (vacante: string, checked: boolean) => void
  onSortByAI: (vacante: string) => void // Added prop for AI sort function
}

export default function CandidateFilters({
  vacantes,
  selectedVacantes,
  onFilterChange,
  onSortByAI, // Destructure new prop
}: CandidateFiltersProps) {
  const handleAISort = () => {
    if (selectedVacantes.length === 1) {
      onSortByAI(selectedVacantes[0])
    }
  }

  return (
    <div className="flex gap-2">
      {" "}
      {/* Added a div to wrap both buttons */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="shrink-0 dark:bg-slate-700 dark:border-slate-600 dark:text-slate-200 dark:hover:bg-slate-600"
          >
            <Filter className="mr-2 h-4 w-4 dark:text-slate-300" />
            Filtrar por Vacante
            {selectedVacantes.length > 0 && (
              <span className="ml-1.5 bg-sky-500 text-white dark:bg-sky-600 dark:text-sky-100 rounded-full px-1.5 py-0.5 text-xs">
                {selectedVacantes.length}
              </span>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64 max-h-80 overflow-y-auto">
          <DropdownMenuLabel>Seleccionar Vacantes</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {vacantes.length > 0 ? (
            vacantes.map((vacante) => (
              <DropdownMenuCheckboxItem
                key={vacante}
                checked={selectedVacantes.includes(vacante)}
                onCheckedChange={(checked) => onFilterChange(vacante, Boolean(checked))}
                onSelect={(e) => e.preventDefault()} // Previene que el menú se cierre al hacer clic
              >
                {vacante}
              </DropdownMenuCheckboxItem>
            ))
          ) : (
            <DropdownMenuLabel className="text-sm text-muted-foreground text-center py-2">
              No hay vacantes para filtrar
            </DropdownMenuLabel>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      {selectedVacantes.length === 1 && ( // Show button only if one vacancy is selected
        <Button
          variant="outline"
          className="shrink-0 neon-button" // Added neon-button class
          onClick={handleAISort}
        >
          <Brain className="mr-2 h-4 w-4" /> {/* Removed dark:text-slate-300 for neon effect */}
          Ordenar con IA
        </Button>
      )}
    </div>
  )
}
