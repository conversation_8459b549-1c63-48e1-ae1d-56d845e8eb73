import LoginForm from "@/components/login/login-form"
import LoginBackground from "@/components/login/login-background"
import { ScrollArea } from "@/components/ui/scroll-area"



export default async function LoginPage() {

  return (
    <main className='w-dvw min-w-dvw h-dvh min-h-dvh'>
      <ScrollArea>
<div className='flex lg:flex-row flex-col'>
  <section className='w-full h-dvh py-8 px-8 sm:px-0 flex flex-col justify-between items-center outline-1 outline-muted border-muted dark:bg-primary-foreground'>
    <article className='flex flex-col gap-8 items-center justify-center h-full'>
      <h1 className='text-center text-primary'>
        Revolucioná tu forma
        <br /> de contratar personal
      </h1>

      <h4 className='text-center text-muted-foreground flex flex-col'>
        <p>Centralizá y ordená tus procesos de selección,</p>
        <p>mejorando cada etapa del ciclo de contratación.</p>
      </h4>

      <div className='w-full'>
        <LoginForm />
      </div>
    </article>
  </section>
  <LoginBackground />
</div>

      </ScrollArea>
    </main>
  )
}