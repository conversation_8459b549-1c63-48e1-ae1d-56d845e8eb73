import { User<PERSON>eycloakAPI } from "@/lib/schemas";

export interface LoginResponse {
  access_token: string;
  token_type: string;
}
export interface UserLoggedIn extends UserKeycloakAPI {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}
export type UserLoggedInWithoutTokens = Omit<
  UserLoggedIn,
  "access_token" | "refresh_token" | "expires_in" | "token_type"
> | null;

export interface DecodedJsonWebToken {
  exp: number
  iat: number
  jti: string
  iss: string
  aud: string
  sub: string
  typ: string
  azp: string
  sid: string
  acr: string
  realm_access: {
    roles: string[]
  }
  resource_access: {
    account: {
      roles: string[]
    }
  }
  scope: string
  email_verified: boolean
  name: string
  preferred_username: string
  given_name: string
  family_name: string
  email: string
  avatar: string
  campaigns_id: string[]
  operators_id: string[]
  [key: string]: unknown
}