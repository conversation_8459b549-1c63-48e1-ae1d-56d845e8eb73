"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  isOpen?: boolean
  onClose?: () => void
  onOpen?: () => void
}

const SidebarContext = React.createContext<{
  isOpen: boolean
  onClose?: () => void
  onOpen?: () => void
}>({
  isOpen: true,
})

const Sidebar = React.forwardRef<HTMLDivElement, SidebarProps>(
  (
    { className, children, isOpen: isOpenProp, onClose, onOpen, ...props },
    ref
  ) => {
    const [isOpenState, setIsOpenState] = React.useState(true)

    const isOpen = isOpenProp !== undefined ? isOpenProp : isOpenState
    const setIsOpen = isOpenProp !== undefined ? (onOpen && onClose ? (open) => (open ? onOpen() : onClose()) : undefined) : setIsOpenState

    React.useEffect(() => {
      if (isOpenProp !== undefined) {
        setIsOpenState(isOpenProp)
      }
    }, [isOpenProp])

    return (
      <SidebarContext.Provider value={{ isOpen, onClose, onOpen }}>
        <div
          ref={ref}
          className={cn(
            "flex flex-col bg-slate-800 border-r border-slate-700 transition-all duration-300 ease-in-out", // Updated background and border
            isOpen ? "w-50" : "w-[78px]", // Reduced width from w-60 to w-50 (200px)
            className
          )}
          {...props}
        >
          {children}
          {setIsOpen && (
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto rounded-full text-slate-300 hover:bg-slate-700 hover:text-slate-100" // Style toggle button
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <ChevronLeft /> : <ChevronRight />}
            </Button>
          )}
        </div>
      </SidebarContext.Provider>
    )
  }
)
Sidebar.displayName = "Sidebar"

const SidebarHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { isOpen } = React.useContext(SidebarContext)
  return (
    <div
      ref={ref}
      className={cn(
        "flex h-16 items-center p-4 transition-all duration-300 ease-in-out border-b border-slate-700", // Added bottom border
        !isOpen && "justify-center",
        className
      )}
      {...props}
    />
  )
})
SidebarHeader.displayName = "SidebarHeader"

const SidebarTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => {
  const { isOpen } = React.useContext(SidebarContext)
  return (
    <h2
      ref={ref}
      className={cn(
        "text-xl font-semibold tracking-tight text-slate-200", // Styled title text
        !isOpen && "hidden",
        className
      )}
      {...props}
    />
  )
})
SidebarTitle.displayName = "SidebarTitle"

const SidebarContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex-1 overflow-y-hidden p-4 text-slate-300 max-h-full h-64", className)} // Styled content text
    {...props}
  />
))
SidebarContent.displayName = "SidebarContent"

const SidebarFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("p-4 border-t border-slate-700", className)} // Added top border to footer
    {...props}
  />
))
SidebarFooter.displayName = "SidebarFooter"

export {
  Sidebar,
  SidebarHeader,
  SidebarTitle,
  SidebarContent,
  SidebarFooter,
}
