"use server"
import { env } from "@/app/env"
import { cookies } from "next/headers"
import { getHost } from "@/lib/actions/host"
import { DecodedJsonWebToken } from "@/types/user"
import { verifyJwt } from "./server-utils"

const AUTH_COOKIE = "session"
const REFRESH_COOKIE = "refresh_token"
const REDIRECT_COOKIE_NAME = "redirect_path"

export interface AuthTokens {
  access_token: string
  refresh_token: string
  token_type: string
}

export async function setAuthCookie(tokens: AuthTokens) {
  const cstore = await cookies()
  const expires = new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 horas para el access token
  const refreshExpires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 días para refresh token

  const value = `${tokens.token_type} ${tokens.access_token}`
  cstore.set(AUTH_COOKIE, value, {
    httpOnly: true,
    secure: false,
    expires,
  })
  cstore.set(REFRESH_COOKIE, tokens.refresh_token, {
    httpOnly: true,
    secure: false,
    expires: refreshExpires,
  })
}

export async function getAuthCookie() {
  const cstore = await cookies()
  const session = cstore.get(AUTH_COOKIE)?.value
  const refresh_token = cstore.get(REFRESH_COOKIE)?.value
  if (!session || !refresh_token) return null
  const [tokenType, accessToken] = session.split(" ")
  return { tokenType, accessToken, refresh_token }
}

export async function getUserCookie(): Promise<DecodedJsonWebToken | null> {
  try {
    const session = await getAuthCookie()
    if (!session || !session.accessToken) return null
    const user = verifyJwt(session.accessToken)
    if (!user) return null
    return user
  } catch (error) {
    console.error("Error parsing user cookie:", error)
    return null
  }
}

export async function removeAuthCookie() {
  const cstore = await cookies()
  cstore.set(AUTH_COOKIE, "", { expires: new Date(0) })
  cstore.set(REFRESH_COOKIE, "", { expires: new Date(0) })
}


export async function setRedirectPathCookie(path: string) {
  const cstore = await cookies()
  cstore.set(REDIRECT_COOKIE_NAME, path, {
    httpOnly: true,
    secure: env.NODE_ENV === "production",
    maxAge: 60 * 5, // 5 minutes
    path: "/",
  })
}

export async function getRedirectPathCookie() {
  const cstore = await cookies()
  return cstore.get(REDIRECT_COOKIE_NAME)?.value || "/dashboard"
}

export async function clearRedirectPathCookie() {
  const cstore = await cookies()
  cstore.set(REDIRECT_COOKIE_NAME, "", { expires: new Date(0) })
}

export async function refreshTokens(
  refreshToken: string,
  setCookies: (tokens: AuthTokens) => Promise<void>
): Promise<boolean> {
  if (!refreshToken) return false
  const url = new URL(`${await getHost("n8n")}/webhook/renew-token`)

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        refresh_token: refreshToken,
      }),
    })

    if (!response.ok) {
      return false
    }

    const tokens: AuthTokens = await response.json()

    // Validar que los tokens recibidos son válidos
    if (!tokens.access_token || !tokens.refresh_token || !tokens.token_type) {
      return false
    }

    await setCookies(tokens)
    return true
  } catch (error) {
    console.error("Error renovando token:", error)
    return false
  }
}
