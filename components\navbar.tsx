"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image"; // Import Image from next/image

import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

export function Navbar() {
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <Link href="/" legacyBehavior passHref>
           <NavigationMenuLink
  className={cn(
    navigationMenuTriggerStyle(),
    "p-0 !bg"
  )}
>
  <Image
    src="/favicon.ico"
    alt="Logo"
    width={32}
    height={32}
    className="h-8 w-8"
  />
</NavigationMenuLink>

          </Link>
        </NavigationMenuItem>
        {/* Removed other navigation items and dropdowns */}
      </NavigationMenuList>
    </NavigationMenu>
  );
}

// ListItem component is no longer needed as dropdowns are removed.
// If you had a ListItem component here, it can be deleted.
