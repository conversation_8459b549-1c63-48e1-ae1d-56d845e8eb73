import { useState, useEffect } from "react";

export function useLocalStorage<T>(key?: string, initialValue?: T) {
  const [storedValue, setStoredValue] = useState<T>(() => initialValue as T);

  useEffect(() => {
    if(!initialValue || !key)return
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        setStoredValue(JSON.parse(item));
      }
    } catch (error) {
      console.warn(`Error reading localStorage key “${key}”:`, error);
    }
  }, [key]);

  const setValue = (value: T | ((val: T) => T)) => {
    if(!initialValue || !key)return

    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.warn(`Error setting localStorage key “${key}”:`, error);
    }
  };

  const remove = () => {
    if(!initialValue || !key)return

    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.warn(`Error removing localStorage key “${key}”:`, error);
    }
  };

  return {storedValue, setValue, remove };
}
