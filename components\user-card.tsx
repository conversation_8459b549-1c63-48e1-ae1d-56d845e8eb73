"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  User,
  Mail,
  Phone,
  MapPin,
  DollarSign,
  FileText,
  Calendar,
  Users,
  X,
  Edit,
  CheckCircle,
  Building,
} from "lucide-react"

interface UserDetailsProps {
  user?: {
    name: string
    role: string
    email: string
    phone: string
    location: string
    salary: string
    source: string
    stage: number
    match: number
    description: string
    isActive: boolean
    emailVerified: boolean
    createdAt: string
    lastUpdated: string
  }
}

export default function UserCard({ user }: UserDetailsProps) {
  const defaultUser = {
    name: "<PERSON>a Gauna",
    role: "Administrador Fortinet Sr",
    email: "<EMAIL>",
    phone: "1171048193",
    location: "No encontrado",
    salary: "no especificado",
    source: "computrabajo",
    stage: 1,
    match: 21.08,
    description: "no encontrada",
    isActive: true,
    emailVerified: false,
    createdAt: "20/6/2025, 12:50:22",
    lastUpdated: "2025-06-26",
  }

  const userData = user || defaultUser

  return (
    <Card className="w-full max-w-2xl bg-gray-900 border-gray-700 text-white">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="flex items-center gap-2 text-lg font-medium">
          <User className="h-5 w-5" />
          Detalles de {userData.name}
        </CardTitle>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-white">
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Primera fila - Nombre y Usuario */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <User className="h-4 w-4" />
              Nombre
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white">{userData.name}</span>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                <Edit className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Building className="h-4 w-4" />
              Rol
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white">{userData.role}</span>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                <Edit className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Segunda fila - Email y Estado */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Mail className="h-4 w-4" />
              Email
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white">{userData.email}</span>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                <Edit className="h-3 w-3" />
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <CheckCircle className="h-4 w-4" />
              Estado
            </div>
            <div className="flex items-center gap-2">
              {userData.isActive ? (
                <>
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-500">Activo</span>
                </>
              ) : (
                <>
                  <div className="h-2 w-2 bg-red-500 rounded-full"></div>
                  <span className="text-red-500">Inactivo</span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Tercera fila - Email verificado y Teléfono */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Mail className="h-4 w-4" />
              Email verificado
            </div>
            <span className={userData.emailVerified ? "text-green-500" : "text-red-500"}>
              {userData.emailVerified ? "Sí" : "No"}
            </span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Phone className="h-4 w-4" />
              Teléfono
            </div>
            <span className="text-white">{userData.phone}</span>
          </div>
        </div>

        {/* Cuarta fila - Ubicación y Salario */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <MapPin className="h-4 w-4" />
              Ubicación
            </div>
            <span className="text-gray-400">{userData.location}</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <DollarSign className="h-4 w-4" />
              Salario
            </div>
            <span className="text-gray-400">{userData.salary}</span>
          </div>
        </div>

        {/* Quinta fila - Fuente y Match */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <FileText className="h-4 w-4" />
              Fuente
            </div>
            <span className="text-white">{userData.source}</span>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Users className="h-4 w-4" />
              Match
            </div>
            <span className="text-green-500">{userData.match}%</span>
          </div>
        </div>

        {/* Sexta fila - Etapa y Creado */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <FileText className="h-4 w-4" />
              Etapa
            </div>
            <Badge variant="secondary" className="bg-gray-700 text-white">
              {userData.stage}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-400">
              <Calendar className="h-4 w-4" />
              Creado
            </div>
            <span className="text-white">{userData.createdAt}</span>
          </div>
        </div>

        {/* Descripción */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <FileText className="h-4 w-4" />
            Descripción
          </div>
          <span className="text-gray-400">{userData.description}</span>
        </div>

        <Separator className="bg-gray-700" />

        {/* Información adicional */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Calendar className="h-4 w-4" />
            Actualizado
          </div>
          <span className="text-gray-400">{userData.lastUpdated}</span>
        </div>

        {/* Botones de acción */}
        <div className="flex  gap-3 pt-4">
          <Button variant="outline" className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800 bg-transparent">
            <FileText className="h-4 w-4 mr-2" />
            CV no disp.
          </Button>
          <Button className="flex-1 bg-blue-600 hover:bg-blue-700">
            <Mail className="h-4 w-4 mr-2" />
            Contactar
          </Button>
          <Button variant="destructive" className="flex-1">
            <X className="h-4 w-4 mr-2" />
            Rechazar
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
 